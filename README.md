# موقع قناة Roberto للروبلوكس 🎮

موقع ويب حديث ومتجاوب لقناة Roberto المتخصصة في محتوى الروبلوكس باللغة العربية.

## المميزات ✨

- **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة (الهاتف، التابلت، الكمبيوتر)
- **دعم اللغتين**: العربية والإنجليزية مع إمكانية التبديل بينهما
- **تصميم حديث**: استخدام أحدث تقنيات CSS مع تأثيرات بصرية جذابة
- **سهولة التنقل**: قائمة تنقل ثابتة مع تمرير سلس
- **تفاعلي**: تأثيرات حركية وتفاعلية عند التمرير والنقر

## أقسام الموقع 📋

1. **الرئيسية**: صفحة ترحيبية مع رابط الاشتراك في القناة
2. **من نحن**: معلومات عن القناة وإحصائيات
3. **الفيديوهات**: عرض أحدث الفيديوهات
4. **الروابط**: روابط مفيدة مقسمة حسب الفئات
5. **التواصل**: معلومات التواصل ونموذج إرسال رسالة

## كيفية الاستخدام 🚀

1. **فتح الموقع**: افتح ملف `index.html` في أي متصفح ويب
2. **التنقل**: استخدم القائمة العلوية للتنقل بين الأقسام
3. **تغيير اللغة**: اضغط على زر "EN" أو "AR" لتغيير اللغة
4. **الهاتف المحمول**: اضغط على أيقونة القائمة (☰) للوصول للتنقل

## التخصيص 🎨

### تغيير المعلومات الشخصية:

1. **اسم القناة**: ابحث عن "Roberto" في `index.html` واستبدله
2. **رابط القناة**: غيّر `https://youtube.com/@Roberto_EG` إلى رابط قناتك
3. **معلومات التواصل**: حدّث معلومات التواصل في قسم Contact

### تغيير الألوان:

في ملف `style.css`، يمكنك تغيير الألوان الأساسية:

```css
:root {
    --primary-color: #ff0000;    /* لون أحمر اليوتيوب */
    --secondary-color: #667eea;  /* لون أزرق متدرج */
    --accent-color: #764ba2;     /* لون بنفسجي */
}
```

### إضافة فيديوهات حقيقية:

1. احصل على معرف الفيديو من رابط اليوتيوب
2. استبدل الكروت الوهمية بروابط حقيقية
3. أضف صور مصغرة حقيقية للفيديوهات

## الملفات المطلوبة 📁

- `index.html` - الصفحة الرئيسية
- `style.css` - ملف التنسيقات
- `script.js` - ملف الجافاسكريبت للتفاعل

## المكتبات المستخدمة 📚

- **Font Awesome**: للأيقونات
- **Google Fonts (Cairo)**: للخط العربي
- **CSS Grid & Flexbox**: للتخطيط المتجاوب

## نصائح للتطوير 💡

1. **إضافة فيديوهات**: يمكنك ربط الموقع بـ YouTube API لجلب الفيديوهات تلقائياً
2. **تحسين SEO**: أضف meta tags مناسبة لمحركات البحث
3. **إضافة مدونة**: يمكن إضافة قسم مدونة للمقالات
4. **نظام تعليقات**: إضافة نظام تعليقات للتفاعل مع الزوار

## الدعم والمساعدة 🆘

إذا واجهت أي مشاكل أو تحتاج لتخصيصات إضافية، يمكنك:

1. تعديل الكود حسب احتياجاتك
2. إضافة المزيد من الأقسام
3. تحسين التصميم والألوان
4. إضافة المزيد من التفاعلية

## الترخيص 📄

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله بحرية.

---

**صُنع بـ ❤️ لمجتمع الروبلوكس العربي**

🎮 **Roberto - أفضل محتوى الروبلوكس باللغة العربية** 🎮
