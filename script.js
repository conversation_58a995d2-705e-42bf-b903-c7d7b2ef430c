// Mobile Navigation Toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
    hamburger.classList.remove('active');
    navMenu.classList.remove('active');
}));

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Language Toggle
let isArabic = true;

const translations = {
    ar: {
        home: 'الرئيسية',
        about: 'من نحن',
        videos: 'الفيديوهات',
        links: 'الروابط',
        contact: 'التواصل',
        heroTitle: 'مرحباً بكم في قناة Roberto',
        heroSubtitle: 'أفضل محتوى الروبلوكس باللغة العربية',
        subscribe: 'اشترك في القناة',
        watchVideos: 'شاهد الفيديوهات',
        aboutTitle: 'من نحن',
        aboutHeading: 'Roberto - قناة الروبلوكس الأولى',
        aboutText: 'نحن نقدم أفضل محتوى الروبلوكس باللغة العربية. من الشروحات التعليمية إلى اللعب الممتع، ستجد كل ما تحتاجه لتصبح محترف في عالم الروبلوكس.',
        subscribers: 'مشترك',
        videos: 'فيديو',
        views: 'مشاهدة',
        latestVideos: 'أحدث الفيديوهات',
        usefulLinks: 'الروابط المفيدة',
        contactUs: 'تواصل معنا',
        sendMessage: 'إرسال الرسالة',
        allRightsReserved: 'جميع الحقوق محفوظة'
    },
    en: {
        home: 'Home',
        about: 'About',
        videos: 'Videos',
        links: 'Links',
        contact: 'Contact',
        heroTitle: 'Welcome to Roberto Channel',
        heroSubtitle: 'Best Roblox Content in Arabic',
        subscribe: 'Subscribe to Channel',
        watchVideos: 'Watch Videos',
        aboutTitle: 'About Us',
        aboutHeading: 'Roberto - The First Roblox Channel',
        aboutText: 'We provide the best Roblox content in Arabic. From educational tutorials to fun gameplay, you\'ll find everything you need to become a pro in the Roblox world.',
        subscribers: 'Subscribers',
        videos: 'Videos',
        views: 'Views',
        latestVideos: 'Latest Videos',
        usefulLinks: 'Useful Links',
        contactUs: 'Contact Us',
        sendMessage: 'Send Message',
        allRightsReserved: 'All Rights Reserved'
    }
};

function toggleLanguage() {
    isArabic = !isArabic;
    const lang = isArabic ? 'ar' : 'en';
    const dir = isArabic ? 'rtl' : 'ltr';
    
    document.documentElement.lang = lang;
    document.documentElement.dir = dir;
    
    // Update navigation
    document.querySelector('.nav-link[href="#home"]').textContent = translations[lang].home;
    document.querySelector('.nav-link[href="#about"]').textContent = translations[lang].about;
    document.querySelector('.nav-link[href="#videos"]').textContent = translations[lang].videos;
    document.querySelector('.nav-link[href="#links"]').textContent = translations[lang].links;
    document.querySelector('.nav-link[href="#contact"]').textContent = translations[lang].contact;
    
    // Update language toggle button
    document.querySelector('.lang-toggle').textContent = isArabic ? 'EN' : 'AR';
    
    // Update hero section
    document.querySelector('.hero-title').textContent = translations[lang].heroTitle;
    document.querySelector('.hero-subtitle').textContent = translations[lang].heroSubtitle;
    
    // Update buttons
    const subscribeBtn = document.querySelector('.btn-primary');
    subscribeBtn.innerHTML = `<i class="fab fa-youtube"></i> ${translations[lang].subscribe}`;
    document.querySelector('.btn-secondary').textContent = translations[lang].watchVideos;
    
    // Update section titles
    document.querySelectorAll('.section-title')[0].textContent = translations[lang].aboutTitle;
    document.querySelectorAll('.section-title')[1].textContent = translations[lang].latestVideos;
    document.querySelectorAll('.section-title')[2].textContent = translations[lang].usefulLinks;
    document.querySelectorAll('.section-title')[3].textContent = translations[lang].contactUs;
    
    // Update about section
    document.querySelector('.about-text h3').textContent = translations[lang].aboutHeading;
    document.querySelector('.about-text p').textContent = translations[lang].aboutText;
    
    // Update stats labels
    const statLabels = document.querySelectorAll('.stat-label');
    statLabels[0].textContent = translations[lang].subscribers;
    statLabels[1].textContent = translations[lang].videos;
    statLabels[2].textContent = translations[lang].views;
    
    // Update contact form button
    document.querySelector('.contact-form button').textContent = translations[lang].sendMessage;
    
    // Update footer
    document.querySelector('.footer p').innerHTML = `&copy; 2024 Roberto. ${translations[lang].allRightsReserved}.`;
}

// Scroll animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('loaded');
        }
    });
}, observerOptions);

// Observe all sections for animations
document.addEventListener('DOMContentLoaded', () => {
    const sections = document.querySelectorAll('section, .video-card, .stat, .link-category, .contact-item');
    sections.forEach(section => {
        section.classList.add('loading');
        observer.observe(section);
    });
});

// Active navigation link highlighting
window.addEventListener('scroll', () => {
    const sections = document.querySelectorAll('section');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Contact form handling
document.querySelector('.contact-form form').addEventListener('submit', (e) => {
    e.preventDefault();
    
    // Get form data
    const formData = new FormData(e.target);
    const name = formData.get('name') || e.target.querySelector('input[type="text"]').value;
    const email = formData.get('email') || e.target.querySelector('input[type="email"]').value;
    const message = formData.get('message') || e.target.querySelector('textarea').value;
    
    // Simple validation
    if (!name || !email || !message) {
        alert(isArabic ? 'يرجى ملء جميع الحقول' : 'Please fill all fields');
        return;
    }
    
    // Simulate form submission
    alert(isArabic ? 'تم إرسال رسالتك بنجاح!' : 'Your message has been sent successfully!');
    e.target.reset();
});

// Video card click handlers
document.querySelectorAll('.video-card').forEach(card => {
    card.addEventListener('click', () => {
        // In a real implementation, this would open the actual YouTube video
        const videoTitle = card.querySelector('h3').textContent;
        alert(isArabic ? 
            `سيتم فتح فيديو: ${videoTitle}` : 
            `Opening video: ${videoTitle}`
        );
    });
});

// Add some interactive effects
document.querySelectorAll('.stat').forEach(stat => {
    stat.addEventListener('mouseenter', () => {
        stat.style.transform = 'translateY(-10px) scale(1.05)';
    });
    
    stat.addEventListener('mouseleave', () => {
        stat.style.transform = 'translateY(-10px) scale(1)';
    });
});

// Parallax effect for hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const hero = document.querySelector('.hero');
    const rate = scrolled * -0.5;
    
    if (hero) {
        hero.style.transform = `translateY(${rate}px)`;
    }
});

// Add loading animation for page load
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
});

// Add some fun Easter eggs
let clickCount = 0;
document.querySelector('.nav-logo').addEventListener('click', () => {
    clickCount++;
    if (clickCount === 5) {
        alert(isArabic ? 
            '🎮 مرحباً بك في عالم الروبلوكس الرائع! 🎮' : 
            '🎮 Welcome to the amazing world of Roblox! 🎮'
        );
        clickCount = 0;
    }
});

console.log('🎮 Roberto YouTube Channel Website Loaded! 🎮');
console.log('Made with ❤️ for the Roblox community');
